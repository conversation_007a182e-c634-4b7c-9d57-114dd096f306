import hero_image from "@/assets/images/hero.jpg";
import vision_image from "@/assets/images/vision.jpg";
import Slider from "react-slick";

const vision_settings = {
  dots: true,
  infinite: false,
  speed: 800,
  autoplay: true,
  autoplaySpeed: 4000,
  slidesToShow: 1,
  slidesToScroll: 1,
  initialSlide: 0,
  rtl: true,
  arrows: false,
  appendDots: (dots: React.ReactNode) => (
    // Change dots position with Tailwind utilities
    <div>
      <ul className="absolute end-[30px] bottom-[100px] flex gap-2">{dots}</ul>
    </div>
  ),
  customPaging: () => (
    // Default dot shape
    <div className="h-3 w-3 rounded-full bg-[#A7A7A7] transition-all duration-300" />
  ),
};

export default function OurVision() {
  return (
    <div className="relative">
      <img
        src={hero_image}
        alt=""
        className="absolute -z-1 h-full w-full object-cover"
      />

      <div className="absolute inset-0 -z-1 bg-[#FFFFFFD9]" />

      <div className="container py-20">
        <h2 className="bg-gradient-to-r from-[#035859] to-[#599379] bg-clip-text text-[48px] font-[600] text-transparent">
          رؤيتنا
        </h2>
        <p className="text-[18px] font-[400] text-[#121217]">
          تطوير وتأهيل غابات وطنية منتجة، مزدهرة، ومستدامة تُسهم في تحقيق
          التوازن البيئي وتعزز جودة الحياة في المملكة.
        </p>

        <Slider {...vision_settings} className="vision-slider mt-10">
          {[...new Array(4)].map((_, index) => {
            return (
              <div key={index}>
                <div className="flex items-center gap-5">
                  <div className="hidden lg:block">
                    <img
                      src={vision_image}
                      alt=""
                      className="h-[500px] w-[500px] max-w-full rounded-lg object-cover"
                    />
                  </div>
                  <div dir="rtl" className="flex flex-col gap-5">
                    <h2 className="bg-gradient-to-r from-[#035859] to-[#599379] bg-clip-text text-[30px] font-[600] text-transparent">
                      تتصور الإدارة العامة للغابات مستقبلا تكون فيه غابات
                      المملكة العربية السعودية:
                    </h2>
                    <div>
                      <p>- مراقبة دقيقة وإدارة بعناية</p>
                      <p>- القدرة على الصمود في مواجهة التحديات البيئية</p>
                      <p>- جزء لا يتجزأ من الاستدامة البيئية الوطنية</p>
                      <p>- نموذج للحفظ المبتكر</p>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </Slider>
      </div>
    </div>
  );
}
