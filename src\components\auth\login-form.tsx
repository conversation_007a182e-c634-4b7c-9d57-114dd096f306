import logo from "@/assets/images/logo.svg";

import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import useLoginForm from "@/hooks/use-login-form";

export default function LoginForm() {
  const { form, onSubmit } = useLoginForm();

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <img src={logo} alt="logo" />

        <div className="bg-gradient-to-r from-[#035859] to-[#599379] bg-clip-text text-[30px] font-[600] text-transparent">
          <h1>مرحباً بك</h1>
          <h1>تسجيل الدخول</h1>
        </div>

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-[14px] font-[500] text-[#121217]">
                البريد الإلكترونى
              </FormLabel>
              <FormControl>
                <Input
                  type="email"
                  placeholder="أدخل البريد الإلكترونى"
                  className="h-[48px] border-[#D1D1DB] bg-white shadow-xs"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-[14px] font-[500] text-[#121217]">
                كلمة المرور
              </FormLabel>
              <FormControl>
                <Input
                  type="password"
                  placeholder="أدخل كلمة المرور"
                  className="h-[48px] border-[#D1D1DB] bg-white shadow-xs"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button
          type="submit"
          className="h-[56px] w-full cursor-pointer !bg-[#035859]"
        >
          تسجيل الدخول
        </Button>
      </form>
    </Form>
  );
}
