import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import type { AccordionItemData } from "@/types";
import { useState } from "react";

interface AccordionListProps {
  items: AccordionItemData[];
}

export function AccordionList({ items }: AccordionListProps) {
  const [active, setActive] = useState<string>("");

  return (
    <Accordion
      type="single"
      collapsible
      className="container px-20 py-10 text-[#121217]"
      value={active}
      onValueChange={setActive}
    >
      {items.map((item) => (
        <AccordionItem key={item.value} value={item.value} className="border-0">
          <AccordionTrigger
            className={cn(
              active === item.value && "bg-[#d5d5d5]",
              "px-3 text-[16px] font-[600]",
            )}
          >
            {item.title}
          </AccordionTrigger>
          <AccordionContent className="flex flex-col gap-4 px-3 text-[16px] font-[400]">
            {item.content}
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
}
