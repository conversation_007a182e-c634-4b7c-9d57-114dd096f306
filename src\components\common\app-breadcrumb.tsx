import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  B<PERSON><PERSON><PERSON>b<PERSON><PERSON>,
  Bread<PERSON>rumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import type { Crumb } from "@/types";
import React from "react";
import { Link } from "react-router";

interface AppBreadcrumbProps {
  items: Crumb[];
}

export function AppBreadcrumb({ items }: AppBreadcrumbProps) {
  return (
    <Breadcrumb className="container py-10">
      <BreadcrumbList>
        {items.map((item, index) => (
          <React.Fragment key={index}>
            <BreadcrumbItem>
              {item.isCurrent ? (
                <BreadcrumbPage className="bg-gradient-to-r from-[#035859] to-[#599379] bg-clip-text text-[14px] font-[400] text-transparent">
                  {item.label}
                </BreadcrumbPage>
              ) : (
                <BreadcrumbLink
                  asChild
                  className="text-[14px] font-[400] text-[#6C6C89]"
                >
                  <Link to={item.href!}>{item.label}</Link>
                </BreadcrumbLink>
              )}
            </BreadcrumbItem>
            {index < items.length - 1 && (
              <BreadcrumbSeparator className="size-4 rotate-180" />
            )}
          </React.Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
