import { default as hero_image } from "@/assets/images/hero.jpg";

interface BannerProps {
  title: string;
  description: string;
}

export default function Banner({ description, title }: BannerProps) {
  return (
    <div className="container">
      <div
        className="flex flex-col gap-5 rounded-2xl bg-cover bg-center bg-no-repeat p-8 py-10 text-white"
        style={{
          backgroundImage: `linear-gradient(to bottom, #05313180, #03585980), url(${hero_image})`,
        }}
      >
        <h1 className="text-[48px] font-[600]">{title}</h1>
        <p className="text-[16px] font-[400]">{description}</p>
      </div>
    </div>
  );
}
