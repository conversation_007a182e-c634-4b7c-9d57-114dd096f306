import { URLS } from "@/utils/urls";
import { <PERSON> } from "react-router";

export default function Footer() {
  return (
    <footer className="bg-[#002C2D] py-10">
      <div className="container grid grid-cols-1 gap-10 px-20 text-white md:grid-cols-2 lg:grid-cols-3">
        <div className="flex flex-col gap-5">
          <div className="text-20px] font-[600]">روابط مهمة</div>
          <div className="flex flex-col gap-3 text-[14px] font-[400]">
            <Link target="_blank" to={"https://www.fao.org/home/<USER>"}>
              منظمة الأغذية والزراعة
            </Link>
            <Link
              target="_blank"
              to={"https://www.mewa.gov.sa/ar/Pages/default.aspx"}
            >
              وزارة البيئة والمياة والبلدية
            </Link>
            <Link
              target="_blank"
              to={"https://ncvc.gov.sa/ar/Pages/default.aspx"}
            >
              المركز الوطنى لتنمية الغطاء النباتى ومكافحة التصحر
            </Link>
            <Link
              target="_blank"
              to={"https://ncvc.gov.sa/ar/HowCanWeHelp/SLA/Pages/Privacy.aspx"}
            >
              سياسة الخصوصية
            </Link>
          </div>
        </div>
        <div className="flex flex-col gap-5">
          <div className="text-20px] font-[600]">روابط سريعة</div>
          <div className="flex flex-col gap-3 text-[14px] font-[400]">
            <Link to={URLS.faq}>الأسئلة شائعة</Link>
            <Link to={URLS.quickFacts}>حقائق سريعة</Link>
          </div>
        </div>
        <div className="flex flex-col gap-5">
          <div className="text-20px] font-[600]"> الخدمات </div>
          <div className="flex flex-col gap-3 text-[14px] font-[400]">
            <Link target="_blank" to={"#"}>
              مستكشف الغابات
            </Link>
          </div>
        </div>
      </div>

      <div className="container">
        <p className="mt-10 border-t pt-10 text-center text-[14px] font-[400] text-white">
          جميع الحقوق محفوظة للمركز الوطنى لتنمية الغطاء النباتى ومكافحة التصحر
          <span className="font-bold">© {new Date().getFullYear()}</span>
        </p>
      </div>
    </footer>
  );
}
