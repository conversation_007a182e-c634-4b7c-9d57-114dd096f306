import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { PhoneInput } from "@/components/utils/phone-input";
import useContactUsForm from "@/hooks/use-contact-us-form";

export default function ContactUsForm() {
  const { form, onSubmit } = useContactUsForm();

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 py-10">
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="f_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-[14px] font-[600] text-[#6C6C89]">
                  الاسم الأول
                  <span className="text-[#B42318]">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder="اكتب اسمك الأول"
                    className="h-[40px] bg-white shadow-xs"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="l_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-[14px] font-[600] text-[#6C6C89]">
                  اسم العائلة
                  <span className="text-[#B42318]">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder="اكتب اسم عائلتك"
                    className="h-[40px] bg-white shadow-xs"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-[14px] font-[600] text-[#6C6C89]">
                  البريد الإلكتروني
                  <span className="text-[#B42318]">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder="ادخل البريد"
                    className="h-[40px] bg-white shadow-xs"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-[14px] font-[600] text-[#6C6C89]">
                  رقم الهاتف
                  <span className="text-[#B42318]">*</span>
                </FormLabel>
                <FormControl>
                  <PhoneInput
                    dir="rtl"
                    placeholder="اكتب رقم هاتفك"
                    className="h-[40px] bg-white shadow-xs"
                    {...field}
                    defaultCountry="SA"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="message"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-[14px] font-[600] text-[#6C6C89]">
                كيف يمكننا مساعدتك؟
              </FormLabel>
              <FormControl>
                <Textarea
                  dir="rtl"
                  placeholder="نص توضيحي"
                  className="h-[120px] bg-white shadow-xs"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="h-[40px] cursor-pointer !bg-[#035859]">
          ارسال
        </Button>
      </form>
    </Form>
  );
}
