import app_store_image from "@/assets/images/app_store.svg";
import google_play_image from "@/assets/images/google_play.svg";
import iphone_15 from "@/assets/images/iphone 15.png";
import iphone_16 from "@/assets/images/iphone 16.png";
import { QRCodeCanvas } from "qrcode.react";

export default function ExplorerApp() {
  return (
    <section className="bg-gradient-to-b from-[#024344] to-[#002C2D] py-20">
      <div className="container grid grid-cols-1 gap-4 bg-[#00000033] p-10 lg:grid-cols-2">
        <div className="flex flex-col gap-5 text-white">
          <h2 className="text-[40px] font-[700]">تطبيق مستكشف الغابات</h2>
          <p className="text-[24px] font-[400]">
            يعرض تفاصيل الغابات المحددة على الخرائط، بما في ذلك الحدود،
            الإحداثيات، مصادر المياه، خطوط المياه، شبكة الري، وخطوط التنفيذ.
          </p>
          <div className="flex flex-wrap items-center gap-5">
            <img src={app_store_image} alt="" className="h-[65px]" />
            <img src={google_play_image} alt="" className="h-[65px]" />
            <QRCodeCanvas
              value="https://reactjs.org/"
              className="!h-[65px] !w-[65px] overflow-hidden rounded-lg border"
            />
          </div>
        </div>
        <div className="relative hidden lg:block">
          <div className="absolute inset-0 flex justify-center">
            <img src={iphone_15} alt="" className="-mt-[50px]" />
            <img src={iphone_16} alt="" className="-mt-[100px]" />
          </div>
        </div>
      </div>
    </section>
  );
}
