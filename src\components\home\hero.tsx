import hero_video from "@/assets/videos/hero.mp4";

export default function Hero() {
  return (
    <section className="relative">
      <video
        autoPlay
        loop
        muted
        className="h-screen w-screen -rotate-y-180 object-cover"
      >
        <source src={hero_video} type="video/mp4" />
      </video>
      <div className="absolute inset-0 bg-gradient-to-b from-[#000]/10 to-[#02302C]/90"></div>

      <div className="absolute top-1/2 left-1/2 container flex w-[760px] max-w-full -translate-x-1/2 -translate-y-1/2 flex-col gap-6 text-center text-white">
        <h1 className="text-[72px] font-[700]">نحمي لتستمر الحياة</h1>
        <p className="text-[20px] leading-8 font-[400]">
          نطوّر النظام الوطني لإدارة معلومات الغابات لرصد الغابات وتحليل
          بياناتها باستخدام تقنيات الاستشعار عن بعد ونظم المعلومات الجغرافية
          لمتابعة التغيرات البيئية ودعم قرارات الاستدامة عبر لوحات تفاعلية دقيقة
        </p>
      </div>
    </section>
  );
}
