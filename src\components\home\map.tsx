import area_image from "@/assets/images/area.svg";
import floor_lamp_icon from "@/assets/images/Floor Lamp.svg";
import hero_image from "@/assets/images/hero.jpg";
import PA_image from "@/assets/images/PA.png";
import pulse_icon from "@/assets/images/Pulse 2.svg";
import trees_image from "@/assets/images/trees.svg";
import water_icon from "@/assets/images/Water.svg";
import { cn } from "@/lib/utils";
import { Trees } from "lucide-react";
import { useState } from "react";
import { Link } from "react-router";

export default function Map() {
  const [activeTab, setActiveTab] = useState(1);

  const forestTabs = [
    {
      key: 1,
      label: "غابات جبلية",
      area: "250 كم2",
      trees: "5000",
      icon: pulse_icon,
    },
    {
      key: 2,
      label: "غابات أودية",
      area: "300 كم2",
      trees: "6000",
      icon: floor_lamp_icon,
    },
    {
      key: 3,
      label: "غابات المنجروف",
      area: "350 كم2",
      trees: "7000",
      icon: water_icon,
    },
  ];

  const selectedForest = forestTabs.find((tab) => tab.key === activeTab);

  return (
    <section
      style={{ backgroundImage: `url(${hero_image})` }}
      className="relative flex items-center bg-cover bg-center bg-no-repeat"
    >
      <div className="absolute inset-0 bg-gradient-to-b from-[#000]/10 to-[#02302C]/90"></div>
      <div className="relative z-50 container py-20 text-white">
        <div className="flex flex-col gap-2 text-center">
          <h2 className="text-[55px] font-[600]">مستكشف الغابات </h2>
          <div className="text-[20px] font-[400]">
            تحويل إدارة الغابات من خلال التكنولوجيا والاستدامة
          </div>
          <Link to={"#"} className="text-[20px] font-[400] underline">
            عرض المستكشف الكامل
          </Link>
        </div>

        <div className="my-5 flex items-center justify-center gap-10">
          {forestTabs.map(({ key, label, icon }) => (
            <div key={key}>
              <div
                className={cn(
                  "flex cursor-pointer items-center justify-center gap-1 rounded-lg px-4 py-2 font-[500]",
                  key === activeTab ? "bg-[#035859]" : "bg-[#FFFFFF33]",
                )}
                onClick={() => setActiveTab(key)}
              >
                <img src={icon} alt="" />
                {label}
              </div>
            </div>
          ))}
        </div>

        <div className="relative">
          <img src={PA_image} alt="" />

          <div className="absolute end-0 bottom-0 flex w-[330px] max-w-full flex-col gap-5 rounded-md border border-[#FFFFFF36] bg-gradient-to-l from-[#035859] to-[#04595A12] p-5">
            <div className="rounded-md bg-gradient-to-l from-[#FFFFFF]/30 to-[#FFFFFF00] p-2">
              <div className="flex items-center gap-2 text-[20px] font-[600]">
                <Trees />
                {/* غابة الخالة */}
                {selectedForest?.label}
              </div>
            </div>

            <div className="flex items-center gap-2">
              <div className="flex flex-1 flex-col items-center justify-center gap-1 rounded-md bg-[#FFFFFF1F] p-3">
                <img src={area_image} alt="" />
                <div className="text-[13px] font-[500]">
                  {selectedForest?.area}
                </div>
                <div className="text-[12px] font-[400] text-[#FFFFFFB2]">
                  المساحة
                </div>
              </div>
              <div className="flex flex-1 flex-col items-center justify-center gap-1 rounded-md bg-[#FFFFFF1F] p-3">
                <img src={trees_image} alt="" />
                <div className="text-[13px] font-[500]">
                  {selectedForest?.trees}
                </div>
                <div className="text-[12px] font-[400] text-[#FFFFFFB2]">
                  عدد الغابات
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
