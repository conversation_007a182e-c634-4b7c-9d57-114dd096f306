import hero_image from "@/assets/images/hero.jpg";
import mission_image from "@/assets/images/mission.jpg";
import { ArrowLeft, ArrowRight, ChevronLeft } from "lucide-react";
import { useRef, useState } from "react";
import Slider from "react-slick";

export default function News() {
  const sliderRef = useRef<Slider>(null);
  const [current, setCurrent] = useState(0);

  const settings = {
    dots: false,
    infinite: false,
    speed: 800,
    autoplay: true,
    autoplaySpeed: 4000,
    slidesToShow: 1,
    slidesToScroll: 1,
    initialSlide: 0,
    rtl: true,
    arrows: false, // we will make custom arrows
    beforeChange: (_: number, next: number) => setCurrent(next),
  };

  const totalSlides = 6;

  return (
    <section className="relative">
      <img
        src={hero_image}
        alt=""
        className="absolute -z-10 h-full w-full object-cover"
      />
      <div className="absolute inset-0 -z-10 bg-[#FFFFFFD9]" />

      <div className="relative container py-20">
        <h2 className="bg-gradient-to-r from-[#035859] to-[#599379] bg-clip-text text-[48px] font-[600] text-transparent">
          الأخبار
        </h2>

        <Slider ref={sliderRef} {...settings} className="mt-10">
          {[...new Array(totalSlides)].map((_, index) => {
            return (
              <div key={index}>
                <div className="grid grid-cols-1 gap-5 lg:grid-cols-2">
                  {/* Right Side Text */}
                  <div dir="rtl" className="flex flex-col gap-10">
                    <div className="text-[20px] font-[600] text-[#6C6C89]">
                      1 ديسمبر, 2024
                      <span className="mt-2 block h-[2px] w-[50px] bg-white"></span>
                    </div>

                    <div className="flex flex-col gap-5">
                      <h2 className="bg-gradient-to-r from-[#035859] to-[#599379] bg-clip-text text-[30px] font-[600] text-transparent">
                        مبادرة السعودية الخضراء تطلق برنامجا ضخما لإعادة التشجير
                      </h2>
                      <div>
                        أطلقت مبادرة السعودية الخضراء برنامجا واسع النطاق لإعادة
                        التشجير لمكافحة التصحر واستعادة النظم البيئية وتعزيز
                        الاستدامة في جميع أنحاء المملكة.
                      </div>
                    </div>

                    <div className="flex cursor-pointer items-center gap-1 bg-gradient-to-r from-[#035859] to-[#599379] bg-clip-text text-[20px] font-[500] text-transparent">
                      التفاصيل
                      <ChevronLeft color="#307D7E" />
                    </div>
                  </div>

                  {/* Left Side Image */}
                  <div className="hidden lg:block">
                    <img
                      src={mission_image}
                      alt=""
                      className="h-[500px] max-w-full rounded-lg object-cover"
                    />
                  </div>
                </div>
              </div>
            );
          })}
        </Slider>

        {/* Custom Arrows + Slide Counter */}
        <div className="absolute bottom-10 left-5 flex w-[50%] flex-row-reverse items-center justify-between gap-5 ps-10">
          <div className="flex items-center gap-3">
            {/* Prev Button */}
            <button
              onClick={() => sliderRef.current?.slickPrev()}
              disabled={current === 0} // 🔥 disable at first slide
              className="cursor-pointer"
            >
              <ArrowRight
                size={20}
                color={current === 0 ? "#6C6C89" : "#035859"}
              />
            </button>

            {/* Next Button */}
            <button
              onClick={() => sliderRef.current?.slickNext()}
              disabled={current === totalSlides - 1} // 🔥 disable at last slide
              className="cursor-pointer"
            >
              <ArrowLeft
                size={20}
                color={current === totalSlides - 1 ? "#6C6C89" : "#035859"}
              />
            </button>
          </div>

          {/* Slide Counter */}
          <span className="text-lg font-semibold text-[#035859]">
            {current + 1} - {totalSlides}
          </span>
        </div>
      </div>
    </section>
  );
}
