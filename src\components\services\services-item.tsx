import type { Service } from "@/types";
import { URLS } from "@/utils/urls";
import { Link } from "react-router";

export default function ServicesItem({
  description,
  image,
  title,
  id,
}: Service) {
  return (
    <Link
      to={`${URLS.services}/${id}`}
      className="flex h-full flex-col gap-4 rounded-lg bg-white p-6 shadow"
    >
      <img src={image} alt={title} className="h-[40px] w-[40px] object-cover" />
      <h3 className="bg-gradient-to-r from-[#035859] to-[#599379] bg-clip-text text-[16px] leading-7 font-[600] text-transparent">
        {title}
      </h3>
      <p className="text-[14px] leading-5 font-[400] text-[#121217]">
        {description}
      </p>
    </Link>
  );
}
