import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

interface ControlledPaginationProps {
  totalPages: number;
  currentPage: number;
  onPageChange: (page: number) => void;
}

export function ControlledPagination({
  totalPages,
  currentPage,
  onPageChange,
}: ControlledPaginationProps) {
  const handlePageChange = (newPage: number) => {
    if (newPage < 1 || newPage > totalPages) return;
    onPageChange(newPage);
  };

  // Helper to decide which pages to render
  const renderPages = () => {
    const pages: (number | "ellipsis")[] = [];

    if (totalPages <= 5) {
      // show all pages if small
      for (let i = 1; i <= totalPages; i++) pages.push(i);
    } else {
      // always show first page
      pages.push(1);

      if (currentPage > 3) pages.push("ellipsis");

      // show neighbors around current
      const start = Math.max(2, currentPage - 1);
      const end = Math.min(totalPages - 1, currentPage + 1);

      for (let i = start; i <= end; i++) pages.push(i);

      if (currentPage < totalPages - 2) pages.push("ellipsis");

      // always show last page
      pages.push(totalPages);
    }

    return pages.map((p, idx) =>
      p === "ellipsis" ? (
        <PaginationItem key={`ellipsis-${idx}`}>
          <PaginationEllipsis />
        </PaginationItem>
      ) : (
        <PaginationItem key={p}>
          <PaginationLink
            href="#"
            isActive={currentPage === p}
            onClick={(e) => {
              e.preventDefault();
              handlePageChange(p);
            }}
          >
            {p}
          </PaginationLink>
        </PaginationItem>
      ),
    );
  };

  return (
    <Pagination className="py-10">
      <PaginationContent>
        {/* Previous */}
        <PaginationItem>
          <PaginationPrevious
            href="#"
            onClick={(e) => {
              e.preventDefault();
              handlePageChange(currentPage - 1);
            }}
          />
        </PaginationItem>

        {renderPages()}

        {/* Next */}
        <PaginationItem>
          <PaginationNext
            href="#"
            onClick={(e) => {
              e.preventDefault();
              handlePageChange(currentPage + 1);
            }}
          />
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
}
