import { transparentNavbarAtom } from "@/atoms/navbar/transparent-navbar";
import { useEffect } from "react";
import { useLocation } from "react-router";

export default function useNavbar() {
  const { pathname } = useLocation();

  const transparentNavbar = transparentNavbarAtom.useValue();

  const handleScroll = () => {
    transparentNavbarAtom.update(window.scrollY < 200);
  };

  useEffect(() => {
    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return {
    transparentNavbar,
    pathname,
  };
}
