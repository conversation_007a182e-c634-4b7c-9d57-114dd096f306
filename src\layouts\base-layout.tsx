import Footer from "@/components/common/footer";
import Navbar from "@/components/common/navbar";
import { useEffect } from "react";
import { Outlet, useLocation } from "react-router";

export default function BaseLayout() {
  const { pathname } = useLocation();

  useEffect(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, [pathname]);

  return (
    <>
      <Navbar />
      <Outlet />
      <Footer />
    </>
  );
}
