import { URLS } from "@/utils/urls";

import { AppBreadcrumb } from "@/components/common/app-breadcrumb";
import ContactUsData from "@/components/contact-us/contact-us-data";
import ContactUsForm from "@/components/contact-us/contact-us-form";
import ContactUsHeading from "@/components/contact-us/contact-us-heading";

export default function ContactUsPage() {
  return (
    <>
      <AppBreadcrumb
        items={[
          { label: "الرئيسية", href: URLS.home },
          { label: "تواصل معنا", isCurrent: true },
        ]}
      />

      <div className="container grid grid-cols-1 gap-10 pb-10 lg:grid-cols-3 lg:pb-0">
        <div className="lg:col-span-2">
          <ContactUsHeading />
          <ContactUsForm />
        </div>
        <ContactUsData />
      </div>
    </>
  );
}
