import { AppBreadcrumb } from "@/components/common/app-breadcrumb";
import Banner from "@/components/common/banner";
import { Button } from "@/components/ui/button";
import { ControlledPagination } from "@/components/utils/controlled-pagination";
import { URLS } from "@/utils/urls";

export default function DocumentationsPage() {
  return (
    <>
      <AppBreadcrumb
        items={[
          { label: "الرئيسية", href: URLS.home },
          { label: "الوثائق", isCurrent: true },
        ]}
      />

      <Banner title="الوثائق" description="ابحث عن جميع المستندات" />

      {/* documents list */}
      <div className="container">
        <div className="flex flex-col gap-5 py-10">
          {[...new Array(5)].map((_, index) => {
            return (
              <div
                className="flex flex-col gap-3 rounded-lg bg-white p-4"
                key={index}
              >
                <h3 className="text-[18px] font-[700]">
                  إدارة اعمال الجرد الوطنى للغابات
                </h3>
                <p className="text-[16px] font-[400]">
                  تصف وثيقة إدارة اعمال الجرد الوطنى للغابات الإجراءات والوسائل
                  التي من خلالها تجمع NFI وتتحقق من صحة وتخزن وتستخدم وتوزع
                  بيانات الجرد الوطني للغابات.
                </p>
                <Button className="w-fit cursor-pointer rounded-sm !bg-[#6DBD99] text-[16px] font-[500] text-[#121217]">
                  تحميل
                </Button>
              </div>
            );
          })}
        </div>
      </div>

      {/* pagination */}
      <ControlledPagination
        totalPages={10}
        currentPage={1}
        onPageChange={(page) => console.log("Page changed to:", page)}
      />
    </>
  );
}
