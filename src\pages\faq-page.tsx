import { AccordionList } from "@/components/common/accordion-list";
import { AppBreadcrumb } from "@/components/common/app-breadcrumb";
import Banner from "@/components/common/banner";
import { faqData } from "@/data/faq";
import { URLS } from "@/utils/urls";

export default function FAQPage() {
  return (
    <>
      <AppBreadcrumb
        items={[
          { label: "الرئيسية", href: URLS.home },
          { label: "الأسئلة الشائعة", isCurrent: true },
        ]}
      />

      <Banner
        title="الأسئلة الشائعة"
        description="ابحث عن إجابات للأسئلة الشائعة حول منتجاتنا وخدماتنا وسياساتنا."
      />

      <AccordionList items={faqData} />
    </>
  );
}
