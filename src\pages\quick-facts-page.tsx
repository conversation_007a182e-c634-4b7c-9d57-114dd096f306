import { AccordionList } from "@/components/common/accordion-list";
import { AppBreadcrumb } from "@/components/common/app-breadcrumb";
import Banner from "@/components/common/banner";
import { quickFactsData } from "@/data/quick-facts";
import { URLS } from "@/utils/urls";

export default function QuickFactsPage() {
  return (
    <>
      <AppBreadcrumb
        items={[
          { label: "الرئيسية", href: URLS.home },
          { label: "معلومات سريعة", isCurrent: true },
        ]}
      />

      <Banner
        title="حقائق سريعة عن الغابات في المملكة العربية السعودية (KSA)"
        description="تعرف على الحقائق السريعة حول غابات المملكة العربية السعودية"
      />

      <AccordionList items={quickFactsData} />
    </>
  );
}
