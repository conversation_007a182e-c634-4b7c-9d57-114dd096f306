export type NavLink = {
  id: number;
  text: string;
  link: string;
};

export type Service = {
  id: number;
  image: string;
  title: string;
  description: string;
  details: {
    description: string;
    steps?: string[];
    beneficiaries: string;
    conditions: string;
  };
};

export interface AccordionItemData {
  value: string;
  title: string;
  content: React.ReactNode;
}

export interface Crumb {
  label: string;
  href?: string;
  isCurrent?: boolean;
}
